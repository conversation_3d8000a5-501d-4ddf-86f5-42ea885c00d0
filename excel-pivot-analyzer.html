<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据透视图处理工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            font-size: 16px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 响应式设计 - 基础布局 */
        @media (max-width: 767px) {
            body {
                font-size: 14px;
            }

            .container {
                padding: 10px;
            }
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            body {
                font-size: 15px;
            }

            .container {
                padding: 15px;
            }
        }

        @media (min-width: 1025px) {
            body {
                font-size: 16px;
            }
        }

        /* 多选下拉菜单样式 */
        .multi-select-container {
            position: relative;
            display: inline-block;
            min-width: 200px;
        }

        .multi-select-button {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            font-size: 1em;
            text-align: left;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 44px;
            transition: border-color 0.3s ease;
        }

        .multi-select-button:hover {
            border-color: #667eea;
        }

        .multi-select-button.open {
            border-color: #667eea;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }

        .multi-select-arrow {
            transition: transform 0.3s ease;
            font-size: 0.8em;
            color: #666;
        }

        .multi-select-button.open .multi-select-arrow {
            transform: rotate(180deg);
        }

        .multi-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #667eea;
            border-top: none;
            border-radius: 0 0 5px 5px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .multi-select-dropdown.open {
            display: block;
        }

        .multi-select-actions {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 8px;
        }

        .multi-select-action-btn {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            background: white;
            cursor: pointer;
            font-size: 0.85em;
            transition: all 0.2s ease;
        }

        .multi-select-action-btn:hover {
            background: #f0f0f0;
            border-color: #667eea;
        }

        .multi-select-option {
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s ease;
            min-height: 36px;
        }

        .multi-select-option:hover {
            background: #f8f9ff;
        }

        .multi-select-option.selected {
            background: #e3f2fd;
            color: #1976d2;
        }

        .multi-select-checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .multi-select-text {
            color: #666;
            font-size: 0.9em;
        }

        .multi-select-count {
            background: #667eea;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.8em;
            margin-left: auto;
        }

        /* 搜索功能样式 */
        .multi-select-search-container {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 8px;
            background: #f8f9fa;
        }

        .multi-select-search {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .multi-select-search:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .multi-select-search-clear {
            width: 24px;
            height: 24px;
            border: none;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
            min-height: 24px;
            min-width: 24px;
        }

        .multi-select-search-clear:hover {
            background: #c82333;
        }

        .multi-select-no-result {
            padding: 12px;
            text-align: center;
            color: #999;
            font-style: italic;
            font-size: 0.9em;
        }

        .multi-select-option mark {
            background: #fff3cd;
            color: #856404;
            padding: 1px 2px;
            border-radius: 2px;
        }

        /* 响应式设计 - 多选组件 */
        @media (max-width: 767px) {
            .multi-select-container {
                min-width: 100%;
                margin-bottom: 10px;
            }

            .multi-select-button {
                padding: 12px 15px;
                font-size: 1.1em;
            }

            .multi-select-dropdown {
                max-height: 150px;
            }

            .multi-select-option {
                padding: 12px 15px;
                min-height: 44px;
            }
        }



        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            line-height: 1.2;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            line-height: 1.4;
        }

        /* 响应式设计 - 头部 */
        @media (max-width: 767px) {
            .header {
                padding: 20px 15px;
                margin-bottom: 20px;
            }

            .header h1 {
                font-size: 1.8em;
                margin-bottom: 8px;
            }

            .header p {
                font-size: 1em;
            }
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            .header {
                padding: 25px;
                margin-bottom: 25px;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .header p {
                font-size: 1.1em;
            }
        }

        .upload-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background-color: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background-color: #f0f2ff;
        }

        .file-input {
            display: none;
        }

        .upload-text {
            font-size: 1.2em;
            color: #667eea;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #666;
            font-size: 0.9em;
        }

        .field-requirements {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 0.9em;
        }

        .field-requirements summary {
            cursor: pointer;
            font-weight: bold;
            color: #667eea;
            padding: 5px 0;
        }

        .field-requirements summary:hover {
            color: #5a67d8;
        }

        .required-fields {
            margin-top: 10px;
            padding: 10px;
            background: white;
            border-radius: 3px;
            border-left: 4px solid #667eea;
        }

        .required-fields h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1em;
        }

        .required-fields ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .required-fields li {
            margin: 5px 0;
            line-height: 1.4;
        }

        .required-fields .note {
            margin: 10px 0 0 0;
            padding: 8px;
            background: #fff3cd;
            color: #856404;
            border-radius: 3px;
            font-size: 0.85em;
        }

        /* 日期区间选择器样式 */
        .date-range-filter {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .date-range-filter label {
            font-weight: bold;
            color: #333;
            font-size: 0.9em;
        }

        .date-input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            min-height: 44px; /* iOS Human Interface Guidelines */
            min-width: 120px;
        }

        .date-input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .filter-apply-btn, .filter-clear-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 0.9em;
            cursor: pointer;
            min-height: 44px; /* iOS Human Interface Guidelines */
            transition: background-color 0.3s ease;
        }

        .filter-apply-btn {
            background: #667eea;
            color: white;
        }

        .filter-apply-btn:hover {
            background: #5a67d8;
        }

        .filter-clear-btn {
            background: #e2e8f0;
            color: #4a5568;
        }

        .filter-clear-btn:hover {
            background: #cbd5e0;
        }

        /* 移动端响应式设计 */
        @media (max-width: 767px) {
            .date-range-filter {
                flex-direction: column;
                align-items: stretch;
            }

            .date-input, .filter-apply-btn, .filter-clear-btn {
                width: 100%;
                margin: 2px 0;
            }
        }

        /* 透视表总计行样式 */
        .pivot-table .total-row {
            background-color: #f8f9fa;
            border-top: 2px solid #007AFF;
            font-weight: bold;
        }

        .pivot-table .total-row td {
            background-color: #f8f9fa;
            border-top: 2px solid #007AFF;
            font-weight: bold;
            color: #333;
        }

        .pivot-table .total-row .total-label {
            background-color: #e9ecef;
            color: #495057;
            text-align: center;
        }

        /* 文本筛选器样式 */
        .text-filter-container {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .text-filter-container label {
            font-weight: bold;
            color: #333;
            font-size: 0.9em;
        }

        .text-filter-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            min-height: 44px; /* iOS Human Interface Guidelines */
            min-width: 200px;
            flex: 1;
        }

        .text-filter-input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .text-filter-input::placeholder {
            color: #999;
            font-style: italic;
        }

        /* 移动端响应式设计 */
        @media (max-width: 767px) {
            .text-filter-container {
                flex-direction: column;
                align-items: stretch;
            }

            .text-filter-input, .filter-clear-btn {
                width: 100%;
                margin: 2px 0;
            }
        }

        .global-filter {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: none;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-label {
            font-weight: bold;
            color: #333;
        }

        .filter-select {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
            min-width: 200px;
        }

        .pivot-tabs {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: none;
        }

        .tab-buttons {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            overflow-x: auto;
        }

        .tab-button {
            padding: 15px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 1em;
            white-space: nowrap;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            min-height: 44px; /* 触摸友好的最小高度 */
            min-width: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }

        /* 响应式设计 - 标签按钮 */
        @media (max-width: 767px) {
            .tab-buttons {
                flex-direction: column;
            }

            .tab-button {
                text-align: left;
                padding: 12px 15px;
                border-bottom: 1px solid #ddd;
                border-left: 3px solid transparent;
                width: 100%;
            }

            .tab-button.active {
                border-bottom-color: #ddd;
                border-left-color: #667eea;
            }
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            .tab-button {
                padding: 12px 16px;
                font-size: 0.95em;
            }
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .pivot-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .pivot-filters {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .copy-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            min-height: 44px; /* 触摸友好的最小高度 */
            min-width: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        /* 响应式设计 - 控件 */
        @media (max-width: 767px) {
            .pivot-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .pivot-filters {
                flex-direction: column;
                gap: 10px;
            }

            .copy-btn {
                width: 100%;
                padding: 14px 20px;
                font-size: 1.1em;
            }
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            .pivot-controls {
                gap: 12px;
            }

            .pivot-filters {
                gap: 12px;
            }

            .copy-btn {
                padding: 11px 22px;
            }
        }

        .table-container {
            max-height: 600px;
            overflow-y: auto;
            overflow-x: auto;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .pivot-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 5px;
            overflow: hidden;
            min-width: 600px; /* 确保表格在小屏幕上可以水平滚动 */
        }

        .pagination-info {
            background: #e3f2fd;
            color: #1976d2;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 0.9em;
            text-align: center;
        }

        .pagination-note {
            background: #fff3e0;
            color: #f57c00;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 0.9em;
            text-align: center;
        }

        /* 响应式设计 - 表格 */
        @media (max-width: 767px) {
            .table-container {
                max-height: 400px;
                margin: 10px -10px; /* 允许表格延伸到容器边缘 */
            }

            .pivot-table {
                font-size: 0.85em;
                min-width: 500px;
            }

            .pivot-table th,
            .pivot-table td {
                padding: 8px 6px;
                font-size: 0.9em;
            }

            .pagination-info,
            .pagination-note {
                font-size: 0.8em;
                padding: 8px;
                margin: 8px 10px;
            }
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            .table-container {
                max-height: 500px;
            }

            .pivot-table {
                font-size: 0.9em;
            }

            .pivot-table th,
            .pivot-table td {
                padding: 10px 8px;
            }
        }

        .pivot-table th,
        .pivot-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .pivot-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .pivot-table tr:hover {
            background: #f8f9ff;
        }

        .number-cell {
            text-align: right;
            font-family: 'Courier New', monospace;
        }

        .percentage-cell {
            text-align: right;
            font-family: 'Courier New', monospace;
            color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-data-message {
            text-align: center;
            padding: 30px 20px;
            color: #999;
            font-size: 1.1em;
            background: #f8f9fa;
            border: 1px dashed #ddd;
            border-radius: 8px;
            margin: 20px;
        }

        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
        }

        .success {
            background: #e6ffe6;
            color: #00b894;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #00b894;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .pivot-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .tab-buttons {
                flex-direction: column;
            }
            
            .tab-button {
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Excel数据透视图处理工具</h1>
            <p>快速处理Excel文件数据并生成多个数据透视图</p>
        </div>

        <div class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-text">📁 点击或拖拽Excel文件到此处</div>
                <div class="upload-hint">支持 .xlsx、.xls 和 .csv 格式文件</div>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls,.csv">
            </div>
            <div class="field-requirements">
                <details>
                    <summary>📋 必需字段要求</summary>
                    <div class="required-fields">
                        <h4>Excel文件必须包含以下字段：</h4>
                        <ul>
                            <li><strong>基础字段</strong>: Driving Region, OTA, Driver, Car Type</li>
                            <li><strong>日期字段</strong>: Pickup Date, Given Date</li>
                            <li><strong>价格字段</strong>: OTA Price (RM), Price (RM)</li>
                            <li><strong>分析字段</strong>: Duty Regions, Driver Name, Remark, Mark</li>
                        </ul>
                        <p class="note">⚠️ 字段名称必须完全匹配，区分大小写</p>
                    </div>
                </details>
            </div>
        </div>

        <div class="global-filter" id="globalFilter">
            <div class="filter-group">
                <span class="filter-label">🌍 全局筛选 - Driving Region:</span>
                <div class="multi-select-container" id="regionFilterContainer">
                    <div class="multi-select-button" id="regionFilterButton">
                        <span class="multi-select-text">所有区域</span>
                        <span class="multi-select-arrow">▼</span>
                    </div>
                    <div class="multi-select-dropdown" id="regionFilterDropdown">
                        <div class="multi-select-actions">
                            <button type="button" class="multi-select-action-btn" onclick="selectAllRegions()">全选</button>
                            <button type="button" class="multi-select-action-btn" onclick="clearAllRegions()">清空</button>
                        </div>
                        <div id="regionFilterOptions"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="pivot-tabs" id="pivotTabs">
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="ota">OTA分析</button>
                <button class="tab-button" data-tab="driver">司机与车型分析</button>
                <button class="tab-button" data-tab="region">区域分布分析</button>
                <button class="tab-button" data-tab="date">日期与备注分析</button>
                <button class="tab-button" data-tab="custom">自定义透视表</button>
            </div>

            <div class="tab-content active" id="ota-tab">
                <div class="pivot-controls">
                    <div class="pivot-filters">
                        <div class="multi-select-container" id="otaFilterContainer">
                            <div class="multi-select-button" id="otaFilterButton">
                                <span class="multi-select-text">所有OTA</span>
                                <span class="multi-select-arrow">▼</span>
                            </div>
                            <div class="multi-select-dropdown" id="otaFilterDropdown">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-action-btn" onclick="selectAllOTA()">全选</button>
                                    <button type="button" class="multi-select-action-btn" onclick="clearAllOTA()">清空</button>
                                </div>
                                <div id="otaFilterOptions"></div>
                            </div>
                        </div>
                        <label>
                            <input type="checkbox" id="pickupDateColumn"> 按Pickup Date分列
                        </label>
                    </div>
                    <button type="button" class="copy-btn" onclick="copyPivotTable('ota')">📋 复制到剪贴板</button>
                </div>
                <div id="ota-table"></div>
            </div>

            <div class="tab-content" id="driver-tab">
                <div class="pivot-controls">
                    <div class="pivot-filters">
                        <div class="multi-select-container" id="driverFilterContainer">
                            <div class="multi-select-button" id="driverFilterButton">
                                <span class="multi-select-text">所有司机</span>
                                <span class="multi-select-arrow">▼</span>
                            </div>
                        </div>
                        <label>
                            <input type="checkbox" id="carTypeColumn"> 按Car Type分列
                        </label>
                    </div>
                    <button class="copy-btn" onclick="copyPivotTable('driver')">📋 复制到剪贴板</button>
                </div>
                <div id="driver-table"></div>
            </div>

            <div class="tab-content" id="region-tab">
                <div class="pivot-controls">
                    <div class="pivot-filters">
                        <div class="text-filter-container">
                            <label for="regionTextFilter">区域筛选:</label>
                            <input type="text" id="regionTextFilter" class="text-filter-input"
                                   placeholder="输入区域名称进行筛选..."
                                   oninput="applyRegionTextFilter()">
                            <button type="button" class="filter-clear-btn" onclick="clearRegionTextFilter()">清空</button>
                        </div>
                    </div>
                    <button class="copy-btn" onclick="copyPivotTable('region')">📋 复制到剪贴板</button>
                </div>
                <div id="region-table"></div>
            </div>

            <div class="tab-content" id="date-tab">
                <div class="pivot-controls">
                    <div class="pivot-filters">
                        <div class="date-range-filter">
                            <label for="startDate">开始日期:</label>
                            <input type="date" id="startDate" class="date-input">
                            <label for="endDate">结束日期:</label>
                            <input type="date" id="endDate" class="date-input">
                            <button type="button" class="filter-apply-btn" onclick="applyDateRangeFilter()">应用筛选</button>
                            <button type="button" class="filter-clear-btn" onclick="clearDateRangeFilter()">清空</button>
                        </div>
                    </div>
                    <button class="copy-btn" onclick="copyPivotTable('date')">📋 复制到剪贴板</button>
                </div>
                <div id="date-table"></div>
            </div>

            <div class="tab-content" id="custom-tab">
                <div class="pivot-controls">
                    <div class="pivot-filters">
                        <select id="customRows" class="filter-select" multiple>
                            <option value="">选择行字段</option>
                        </select>
                        <select id="customColumns" class="filter-select" multiple>
                            <option value="">选择列字段</option>
                        </select>
                        <select id="customValues" class="filter-select" multiple>
                            <option value="">选择值字段</option>
                        </select>
                        <button class="copy-btn" onclick="generateCustomPivot()">生成透视表</button>
                    </div>
                    <button class="copy-btn" onclick="copyPivotTable('custom')">📋 复制到剪贴板</button>
                </div>
                <div id="custom-table"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let rawData = [];
        let filteredData = [];
        let currentPivotData = {};
        let multiSelectInstances = {}; // 存储多选组件实例
        let originalExcelData = []; // 存储原始Excel数据

        /**
         * @class MultiSelectDropdown - 多选下拉菜单组件
         * 提供多选功能的下拉菜单组件
         */
        class MultiSelectDropdown {
            /**
             * @function constructor - 构造函数
             * @param {string} containerId - 容器ID
             * @param {Object} options - 配置选项
             */
            constructor(containerId, options = {}) {
                this.containerId = containerId;
                this.options = options;
                this.selectedValues = new Set();
                this.allOptions = [];
                this.filteredOptions = [];
                this.isOpen = false;
                this.searchTerm = '';

                this.container = document.getElementById(containerId);
                this.button = this.container.querySelector('.multi-select-button');
                this.dropdown = this.container.querySelector('.multi-select-dropdown');
                this.textElement = this.container.querySelector('.multi-select-text');
                this.arrowElement = this.container.querySelector('.multi-select-arrow');
                this.optionsContainer = this.container.querySelector('[id$="Options"]');

                this.init();
            }

            /**
             * @function init - 初始化组件
             */
            init() {
                this.button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggle();
                });

                // 点击外部关闭下拉菜单
                document.addEventListener('click', (e) => {
                    if (!this.container.contains(e.target)) {
                        this.close();
                    }
                });

                this.updateDisplay();
            }

            /**
             * @function setOptions - 设置选项
             * @param {Array} options - 选项数组
             */
            setOptions(options) {
                this.allOptions = options;
                this.filteredOptions = [...options];
                this.searchTerm = '';
                this.renderSearchBox();
                this.renderOptions();
            }

            /**
             * @function renderSearchBox - 渲染搜索框
             */
            renderSearchBox() {
                // 检查是否已存在搜索框
                let searchContainer = this.dropdown.querySelector('.multi-select-search-container');
                if (!searchContainer) {
                    searchContainer = document.createElement('div');
                    searchContainer.className = 'multi-select-search-container';
                    searchContainer.innerHTML = `
                        <input type="text" class="multi-select-search" placeholder="搜索选项..." />
                        <button type="button" class="multi-select-search-clear" title="清空搜索">×</button>
                    `;

                    // 插入到下拉菜单的顶部
                    this.dropdown.insertBefore(searchContainer, this.optionsContainer);

                    // 添加搜索事件监听
                    const searchInput = searchContainer.querySelector('.multi-select-search');
                    const clearButton = searchContainer.querySelector('.multi-select-search-clear');

                    searchInput.addEventListener('input', (e) => {
                        e.stopPropagation();
                        this.handleSearch(e.target.value);
                    });

                    searchInput.addEventListener('keydown', (e) => {
                        e.stopPropagation();
                    });

                    clearButton.addEventListener('click', (e) => {
                        e.stopPropagation();
                        searchInput.value = '';
                        this.handleSearch('');
                        searchInput.focus();
                    });
                }
            }

            /**
             * @function handleSearch - 处理搜索
             * @param {string} searchTerm - 搜索词
             */
            handleSearch(searchTerm) {
                this.searchTerm = searchTerm.toLowerCase();

                if (this.searchTerm === '') {
                    this.filteredOptions = [...this.allOptions];
                } else {
                    this.filteredOptions = this.allOptions.filter(option =>
                        option.label.toLowerCase().includes(this.searchTerm) ||
                        option.value.toLowerCase().includes(this.searchTerm)
                    );
                }

                this.renderOptions();
            }

            /**
             * @function renderOptions - 渲染选项
             */
            renderOptions() {
                this.optionsContainer.innerHTML = '';

                // 如果没有搜索结果，显示提示
                if (this.filteredOptions.length === 0 && this.searchTerm !== '') {
                    const noResultElement = document.createElement('div');
                    noResultElement.className = 'multi-select-no-result';
                    noResultElement.textContent = '未找到匹配的选项';
                    this.optionsContainer.appendChild(noResultElement);
                    return;
                }

                this.filteredOptions.forEach(option => {
                    const optionElement = document.createElement('div');
                    optionElement.className = 'multi-select-option';

                    // 高亮搜索词
                    let labelHtml = option.label;
                    if (this.searchTerm !== '') {
                        const regex = new RegExp(`(${this.searchTerm})`, 'gi');
                        labelHtml = option.label.replace(regex, '<mark>$1</mark>');
                    }

                    optionElement.innerHTML = `
                        <input type="checkbox" class="multi-select-checkbox" value="${option.value}"
                               ${this.selectedValues.has(option.value) ? 'checked' : ''}>
                        <span>${labelHtml}</span>
                    `;

                    const checkbox = optionElement.querySelector('.multi-select-checkbox');
                    checkbox.addEventListener('change', (e) => {
                        e.stopPropagation();
                        this.toggleOption(option.value);
                    });

                    optionElement.addEventListener('click', (e) => {
                        e.stopPropagation();
                        checkbox.checked = !checkbox.checked;
                        this.toggleOption(option.value);
                    });

                    this.optionsContainer.appendChild(optionElement);
                });
            }

            /**
             * @function toggleOption - 切换选项状态
             * @param {string} value - 选项值
             */
            toggleOption(value) {
                if (this.selectedValues.has(value)) {
                    this.selectedValues.delete(value);
                } else {
                    this.selectedValues.add(value);
                }
                this.updateDisplay();
                this.updateOptionStyles();

                // 触发变化事件
                if (this.options.onChange) {
                    this.options.onChange(Array.from(this.selectedValues));
                }
            }

            /**
             * @function updateOptionStyles - 更新选项样式
             */
            updateOptionStyles() {
                const options = this.optionsContainer.querySelectorAll('.multi-select-option');
                options.forEach(option => {
                    const checkbox = option.querySelector('.multi-select-checkbox');
                    if (checkbox.checked) {
                        option.classList.add('selected');
                    } else {
                        option.classList.remove('selected');
                    }
                });
            }

            /**
             * @function updateDisplay - 更新显示文本
             */
            updateDisplay() {
                const count = this.selectedValues.size;
                const total = this.allOptions.length;

                if (count === 0) {
                    this.textElement.innerHTML = this.options.placeholder || '请选择';
                } else if (count === total) {
                    this.textElement.innerHTML = `全部 <span class="multi-select-count">${count}</span>`;
                } else {
                    this.textElement.innerHTML = `已选择 <span class="multi-select-count">${count}</span>`;
                }
            }

            /**
             * @function toggle - 切换下拉菜单显示状态
             */
            toggle() {
                if (this.isOpen) {
                    this.close();
                } else {
                    this.open();
                }
            }

            /**
             * @function open - 打开下拉菜单
             */
            open() {
                this.isOpen = true;
                this.button.classList.add('open');
                this.dropdown.classList.add('open');
                this.arrowElement.style.transform = 'rotate(180deg)';
            }

            /**
             * @function close - 关闭下拉菜单
             */
            close() {
                this.isOpen = false;
                this.button.classList.remove('open');
                this.dropdown.classList.remove('open');
                this.arrowElement.style.transform = 'rotate(0deg)';
            }

            /**
             * @function selectAll - 全选
             */
            selectAll() {
                this.selectedValues.clear();
                this.allOptions.forEach(option => {
                    this.selectedValues.add(option.value);
                });
                this.renderOptions();
                this.updateDisplay();

                if (this.options.onChange) {
                    this.options.onChange(Array.from(this.selectedValues));
                }
            }

            /**
             * @function clearAll - 清空选择
             */
            clearAll() {
                this.selectedValues.clear();
                this.renderOptions();
                this.updateDisplay();

                if (this.options.onChange) {
                    this.options.onChange(Array.from(this.selectedValues));
                }
            }

            /**
             * @function getSelectedValues - 获取选中的值
             * @returns {Array} 选中的值数组
             */
            getSelectedValues() {
                return Array.from(this.selectedValues);
            }

            /**
             * @function setSelectedValues - 设置选中的值
             * @param {Array} values - 要选中的值数组
             */
            setSelectedValues(values) {
                this.selectedValues = new Set(values);
                this.renderOptions();
                this.updateDisplay();
            }
        }

        /**
         * @function initializeApp - 初始化应用程序
         * 设置事件监听器和初始状态
         */
        function initializeApp() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 文件上传事件
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleFileDrop);
            fileInput.addEventListener('change', handleFileSelect);

            // 标签页切换事件
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', () => switchTab(button.dataset.tab));
            });

            // 全局筛选事件
            document.getElementById('regionFilter').addEventListener('change', applyGlobalFilter);
        }

        /**
         * @function handleDragOver - 处理拖拽悬停事件
         * @param {Event} e - 拖拽事件对象
         */
        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        /**
         * @function handleFileDrop - 处理文件拖拽放置事件
         * @param {Event} e - 拖拽事件对象
         */
        function handleFileDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        /**
         * @function handleFileSelect - 处理文件选择事件
         * @param {Event} e - 文件选择事件对象
         */
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                processFile(file);
            }
        }

        /**
         * @function processFile - 处理Excel文件
         * @param {File} file - 要处理的文件对象
         */
        function processFile(file) {
            const isExcel = file.name.match(/\.(xlsx|xls)$/i);
            const isCSV = file.name.match(/\.csv$/i);

            if (!isExcel && !isCSV) {
                showError('请选择有效的Excel文件 (.xlsx 或 .xls) 或 CSV文件 (.csv)');
                return;
            }

            // 检查文件大小限制 (50MB)
            if (file.size > 50 * 1024 * 1024) {
                showError('文件过大，请选择小于50MB的Excel文件');
                return;
            }

            showLoading(isCSV ? '正在解析CSV文件...' : '正在解析Excel文件...');

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    if (isCSV) {
                        // 处理CSV文件
                        const csvText = e.target.result;
                        originalExcelData = parseCSV(csvText);

                        if (originalExcelData.length === 0) {
                            showError('CSV文件中没有找到数据');
                            return;
                        }
                    } else {
                        // 处理Excel文件
                        const data = new Uint8Array(e.target.result);

                    // 使用更安全的Excel解析选项
                    const workbook = XLSX.read(data, {
                        type: 'array',
                        cellDates: true,
                        cellNF: false,
                        cellText: false
                    });

                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    // 限制解析的行数，避免内存溢出
                    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
                    const maxRows = 15000; // 最大处理15000行

                    if (range.e.r > maxRows) {
                        showError(`Excel文件行数过多 (${range.e.r + 1} 行)，最大支持 ${maxRows} 行。请筛选数据后重新上传。`);
                        return;
                    }

                    originalExcelData = XLSX.utils.sheet_to_json(worksheet, {
                        defval: '', // 空单元格默认值
                        blankrows: false // 跳过空行
                    });

                    if (originalExcelData.length === 0) {
                        showError('Excel文件中没有找到数据');
                        return;
                    }

                    }

                    // 通用数据处理逻辑（Excel和CSV都使用）
                    rawData = originalExcelData;

                    // 数据验证和清理
                    rawData = validateAndCleanData(rawData);

                    if (rawData.length === 0) {
                        showError('数据验证失败，请检查文件内容');
                        return;
                    }

                    showLoading('正在初始化筛选器...');
                    setTimeout(() => {
                        initializeFilters();

                        showLoading('正在生成透视表...');
                        setTimeout(() => {
                            generateAllPivotTables();
                            showSuccess(`成功加载 ${rawData.length} 行数据`);

                            // 始终显示透视表界面，每个透视表独立处理字段匹配
                            document.getElementById('globalFilter').style.display = 'block';
                            document.getElementById('pivotTabs').style.display = 'block';
                        }, 100);
                    }, 50);

                } catch (error) {
                    console.error('Excel解析错误:', error);
                    showError('解析Excel文件时出错: ' + error.message + '。请检查文件格式是否正确。');
                }
            };

            reader.onerror = function() {
                showError('读取文件失败，请重试');
            };

            if (isCSV) {
                reader.readAsText(file, 'UTF-8');
            } else {
                reader.readAsArrayBuffer(file);
            }
        }

        /**
         * @function parseCSV - 解析CSV文件内容
         * @param {string} csvText - CSV文件文本内容
         * @returns {Array} 解析后的数据数组
         */
        function parseCSV(csvText) {
            const lines = csvText.split('\n').filter(line => line.trim());
            if (lines.length < 2) {
                return [];
            }

            // 解析表头
            const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));

            // 解析数据行
            const data = [];
            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',').map(value => value.trim().replace(/"/g, ''));
                if (values.length === headers.length) {
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header] = values[index];
                    });
                    data.push(row);
                }
            }

            return data;
        }

        /**
         * @function validateAndCleanData - 验证和清理数据
         * @param {Array} data - 原始数据数组
         * @returns {Array} 清理后的数据数组
         */
        function validateAndCleanData(data) {
            if (!Array.isArray(data) || data.length === 0) {
                return [];
            }

            // 清理和验证数据
            const cleanedData = data.filter((row, index) => {
                // 跳过完全空的行
                const hasData = Object.values(row).some(value =>
                    value !== null && value !== undefined && value !== ''
                );

                if (!hasData) {
                    return false;
                }

                // 清理数值字段 - 动态检测包含价格、金额等关键词的字段
                Object.keys(row).forEach(field => {
                    const fieldLower = field.toLowerCase();
                    if (fieldLower.includes('price') || fieldLower.includes('金额') ||
                        fieldLower.includes('费用') || fieldLower.includes('价格')) {
                        if (row[field] !== undefined && row[field] !== '') {
                            const numValue = parseFloat(row[field]);
                            row[field] = isNaN(numValue) ? 0 : numValue;
                        } else {
                            row[field] = 0;
                        }
                    }
                });

                // 清理文本字段 - 对所有文本字段进行trim处理
                Object.keys(row).forEach(field => {
                    if (row[field] !== undefined && row[field] !== null && typeof row[field] === 'string') {
                        row[field] = String(row[field]).trim();
                    }
                });

                return true;
            });

            console.log(`数据清理完成: ${data.length} -> ${cleanedData.length} 行`);
            return cleanedData;
        }

        /**
         * @function checkRequiredFields - 检查必需字段是否存在
         * @param {Array} fields - 文件中的字段列表
         * @returns {Array} 缺失的必需字段列表
         */
        function checkRequiredFields(fields) {
            const requiredFields = [
                // OTA分析必需字段
                'OTA', 'Pickup Date', 'OTA Price (RM)', 'Price (RM)',
                // 司机与车型分析必需字段
                'Driver', 'Car Type',
                // 区域分布分析必需字段
                'Duty Regions',
                // 日期与备注分析必需字段
                'Given Date', 'Driver Name', 'Remark', 'Mark',
                // 全局筛选必需字段
                'Driving Region'
            ];

            const missingFields = [];
            requiredFields.forEach(field => {
                if (!fields.includes(field)) {
                    missingFields.push(field);
                }
            });

            return missingFields;
        }

        /**
         * @function initializeFilters - 初始化所有筛选器
         * 根据数据填充筛选器选项，使用固定字段名匹配
         */
        function initializeFilters() {
            try {
                // 初始化多选组件
                initializeMultiSelectComponents();

                // 获取所有字段
                const fields = Object.keys(rawData[0] || {});
                console.log('文件字段列表:', fields);

                // 不再进行强制字段验证，允许任何格式的文件处理
                console.log('开始初始化筛选器，支持部分字段匹配');

            // 暂时禁用onChange事件，避免在初始化时触发
            const originalOnChange = {};
            if (multiSelectInstances.regionFilter) {
                originalOnChange.regionFilter = multiSelectInstances.regionFilter.options.onChange;
                multiSelectInstances.regionFilter.options.onChange = () => {};
            }
            if (multiSelectInstances.otaFilter) {
                originalOnChange.otaFilter = multiSelectInstances.otaFilter.options.onChange;
                multiSelectInstances.otaFilter.options.onChange = () => {};
            }

            // 初始化区域筛选器（使用固定字段名）
            if (fields.includes('Driving Region') && multiSelectInstances.regionFilter) {
                const regions = [...new Set(rawData.map(row => row['Driving Region']).filter(Boolean))];
                const regionOptions = regions.map(region => ({ value: region, label: region }));
                multiSelectInstances.regionFilter.setOptions(regionOptions);
            }

            // 初始化OTA筛选器（使用固定字段名）
            if (fields.includes('OTA') && multiSelectInstances.otaFilter) {
                const otas = [...new Set(rawData.map(row => row['OTA']).filter(Boolean))];
                const otaOptions = otas.map(ota => ({ value: ota, label: ota }));
                multiSelectInstances.otaFilter.setOptions(otaOptions);
            }

            // 初始化司机筛选器（使用智能字段匹配）
            const driverField = findFieldByPatterns(fields, [
                'Driver', 'Driver Name', 'DriverName', 'Name', 'Full Name',
                'driver', 'driver name', 'drivername', 'name', 'full name',
                '司机', '司机姓名', '驾驶员', '驾驶员姓名', '姓名'
            ]);

            console.log('司机字段匹配结果:', driverField);
            console.log('可用字段:', fields);

            if (driverField) {
                const drivers = [...new Set(rawData.map(row => row[driverField]).filter(Boolean))];
                const driverOptions = drivers.map(driver => ({ value: driver, label: driver }));
                console.log('司机数据:', drivers);

                if (multiSelectInstances.driverFilter) {
                    multiSelectInstances.driverFilter.setOptions(driverOptions);
                    console.log('司机筛选器数据已设置');
                } else {
                    console.error('司机筛选器实例不存在');
                }
            } else {
                console.log('未找到司机字段');
            }

            // 区域分布分析使用文本筛选器，不需要填充下拉菜单数据
            // 文本筛选器直接对原始数据进行筛选

            // 初始化日期区间选择器（使用固定字段名）
            if (fields.includes('Given Date')) {
                initializeDateRangeFilter();
            }

            // 初始化自定义透视表字段选择器
            const customRows = document.getElementById('customRows');
            const customColumns = document.getElementById('customColumns');
            const customValues = document.getElementById('customValues');

            const fieldOptions = fields.map(field => `<option value="${field}">${field}</option>`).join('');
            customRows.innerHTML = fieldOptions;
            customColumns.innerHTML = fieldOptions;
            customValues.innerHTML = fieldOptions;

            // 设置复选框事件监听
            setupCheckboxListeners();

            // 恢复onChange事件
            setTimeout(() => {
                if (multiSelectInstances.regionFilter && originalOnChange.regionFilter) {
                    multiSelectInstances.regionFilter.options.onChange = originalOnChange.regionFilter;
                }
                if (multiSelectInstances.otaFilter && originalOnChange.otaFilter) {
                    multiSelectInstances.otaFilter.options.onChange = originalOnChange.otaFilter;
                }
            }, 100);

            return true; // 返回成功状态

        } catch (error) {
            console.error('初始化筛选器时出错:', error);
            showError('初始化筛选器时出错，请检查文件格式');
            return false;
        }
    }

        /**
         * @function setupCheckboxListeners - 设置复选框事件监听器
         * 为透视表配置复选框添加事件监听
         */
        function setupCheckboxListeners() {
            // Pickup Date列显示复选框
            const pickupDateCheckbox = document.getElementById('pickupDateColumn');
            if (pickupDateCheckbox) {
                pickupDateCheckbox.addEventListener('change', () => {
                    generatePivotTable('ota');
                });
            }

            // Car Type列显示复选框
            const carTypeCheckbox = document.getElementById('carTypeColumn');
            if (carTypeCheckbox) {
                carTypeCheckbox.addEventListener('change', () => {
                    generatePivotTable('driver');
                });
            }
        }

        /**
         * @function applyGlobalFilter - 应用全局筛选
         * 根据Driving Region筛选数据
         */
        function applyGlobalFilter() {
            let selectedRegions = [];
            if (multiSelectInstances.regionFilter) {
                selectedRegions = multiSelectInstances.regionFilter.getSelectedValues();
            }

            if (selectedRegions.length > 0) {
                // 使用固定字段名
                filteredData = rawData.filter(row =>
                    selectedRegions.includes(row['Driving Region'])
                );
            } else {
                filteredData = [...rawData];
            }
            // 直接生成透视表，避免递归调用
            generateAllPivotTablesWithoutEvents();
        }

        /**
         * @function generateAllPivotTables - 生成所有透视表
         * 初始化时调用，设置初始筛选数据
         */
        function generateAllPivotTables() {
            // 检查数据量限制
            if (rawData.length > 10000) {
                showError(`数据量过大 (${rawData.length} 行)，建议分批处理或筛选后再分析。最大支持10000行数据。`);
                return;
            }

            filteredData = [...rawData];
            // 直接生成透视表，不调用applyGlobalFilter避免递归
            generateAllPivotTablesWithoutEvents();
        }

        /**
         * @function generateAllPivotTablesInternal - 内部生成所有透视表
         * 避免递归调用的内部实现
         */
        function generateAllPivotTablesInternal() {
            try {
                // 使用setTimeout分批处理，避免阻塞UI
                setTimeout(() => generatePivotTable('ota'), 0);
                setTimeout(() => generatePivotTable('driver'), 10);
                setTimeout(() => generatePivotTable('region'), 20);
                setTimeout(() => generatePivotTable('date'), 30);
            } catch (error) {
                console.error('生成透视表时出错:', error);
                showError('生成透视表时出错，请检查数据格式或减少数据量');
            }
        }

        /**
         * @function generateAllPivotTablesWithoutEvents - 生成所有透视表（不触发事件）
         * 用于全局筛选时避免递归调用
         */
        function generateAllPivotTablesWithoutEvents() {
            try {
                // 直接调用透视表生成函数，不通过事件系统
                setTimeout(() => {
                    const otaResult = generateOTAPivot(filteredData);
                    displayPivotTable('ota', otaResult);
                }, 0);

                setTimeout(() => {
                    const driverResult = generateDriverPivot(filteredData);
                    displayPivotTable('driver', driverResult);
                }, 10);

                setTimeout(() => {
                    const regionResult = generateRegionPivot(filteredData);
                    displayPivotTable('region', regionResult);
                }, 20);

                setTimeout(() => {
                    const dateResult = generateDatePivot(filteredData);
                    displayPivotTable('date', dateResult);
                }, 30);
            } catch (error) {
                console.error('生成透视表时出错:', error);
                showError('生成透视表时出错，请检查数据格式或减少数据量');
            }
        }

        /**
         * @function displayPivotTable - 显示透视表（简化版渲染）
         * @param {string} type - 透视表类型
         * @param {Object} pivotData - 透视表数据
         */
        function displayPivotTable(type, pivotData) {
            // 存储数据到全局变量
            currentPivotData[type] = pivotData;
            // 调用完整的渲染函数
            renderPivotTable(type, pivotData);
        }

        /**
         * @function switchTab - 切换标签页
         * @param {string} tabName - 标签页名称
         */
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // 激活选中的标签页
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
        }

        /**
         * @function generatePivotTable - 生成指定类型的透视表
         * @param {string} type - 透视表类型
         */
        function generatePivotTable(type) {
            try {
                // 检查数据是否存在
                if (!filteredData || filteredData.length === 0) {
                    const container = document.getElementById(`${type}-table`);
                    if (container) {
                        container.innerHTML = '<div class="loading">暂无数据</div>';
                    }
                    return;
                }

                // 对于大数据集，显示处理提示
                if (filteredData.length > 5000) {
                    const container = document.getElementById(`${type}-table`);
                    if (container) {
                        container.innerHTML = '<div class="loading">正在处理大数据集，请稍候...</div>';
                    }
                }

                // 使用浅拷贝避免深度拷贝的性能问题
                let data = filteredData.slice();
                let pivotData = {};

                switch (type) {
                    case 'ota':
                        pivotData = generateOTAPivot(data);
                        break;
                    case 'driver':
                        pivotData = generateDriverPivot(data);
                        break;
                    case 'region':
                        pivotData = generateRegionPivot(data);
                        break;
                    case 'date':
                        pivotData = generateDatePivot(data);
                        break;
                    default:
                        console.warn(`未知的透视表类型: ${type}`);
                        return;
                }

                currentPivotData[type] = pivotData;

                // 对于大数据集，使用异步渲染
                if (filteredData.length > 5000) {
                    setTimeout(() => renderPivotTable(type, pivotData), 0);
                } else {
                    renderPivotTable(type, pivotData);
                }

            } catch (error) {
                console.error(`生成${type}透视表时出错:`, error);
                const container = document.getElementById(`${type}-table`);
                if (container) {
                    container.innerHTML = `<div class="error">生成透视表时出错: ${error.message}</div>`;
                }
            }
        }

        /**
         * @function generateOTAPivot - 生成OTA分析透视表
         * @param {Array} data - 数据数组
         * @returns {Object} 透视表数据
         */
        function generateOTAPivot(data) {
            try {
                // 检查OTA分析的必需字段
                const fields = Object.keys(data[0] || {});
                const requiredFields = ['OTA', 'Pickup Date', 'OTA Price (RM)', 'Price (RM)'];
                const missingFields = requiredFields.filter(field => !fields.includes(field));

                if (missingFields.length > 0) {
                    console.log('OTA分析缺少字段:', missingFields);
                    return {
                        headers: ['提示'],
                        rows: [{ message: '无数据可显示 - 缺少必需字段' }],
                        noData: true
                    };
                }

                // 使用固定字段名
                const otaField = 'OTA';
                const dateField = 'Pickup Date';
                const otaPriceField = 'OTA Price (RM)';
                const priceField = 'Price (RM)';

                // 应用OTA筛选（多选）
                let selectedOTAs = [];
                if (multiSelectInstances.otaFilter) {
                    selectedOTAs = multiSelectInstances.otaFilter.getSelectedValues();
                }

                if (selectedOTAs.length > 0) {
                    data = data.filter(row => selectedOTAs.includes(row[otaField]));
                }

                const showPickupDate = document.getElementById('pickupDateColumn')?.checked || false;
                const grouped = new Map(); // 使用Map提高性能

                // 分批处理大数据集
                const batchSize = 1000;
                for (let i = 0; i < data.length; i += batchSize) {
                    const batch = data.slice(i, i + batchSize);

                    batch.forEach(row => {
                        const ota = row[otaField] || '未知';
                        const pickupDate = row[dateField] || '未知';
                        const otaPrice = parseFloat(row[otaPriceField]) || 0;
                        const price = parseFloat(row[priceField]) || 0;

                        const key = showPickupDate ? `${ota}|${pickupDate}` : ota;

                        if (!grouped.has(key)) {
                            grouped.set(key, {
                                ota: ota,
                                pickupDate: showPickupDate ? pickupDate : null,
                                count: 0,
                                otaPrice: 0,
                                price: 0
                            });
                        }

                        const item = grouped.get(key);
                        item.count++;
                        item.otaPrice += otaPrice;
                        item.price += price;
                    });
                }

                // 计算利润率并排序
                const result = Array.from(grouped.values()).map(item => ({
                    ...item,
                    profitRate: item.otaPrice > 0 ? (1 - (item.price / item.otaPrice)) * 100 : 0
                })).sort((a, b) => b.count - a.count);

                // 生成固定表头
                const headers = ['OTA'];
                if (showPickupDate) {
                    headers.push('Pickup Date');
                }
                headers.push('计数', 'OTA Price (RM)', 'Price (RM)', '利润率 (%)');

                // 利润率计算公式：1-(Price (RM)/OTA Price (RM))
                // 所有数值按从高到低排序

                return {
                    headers,
                    rows: result
                };
            } catch (error) {
                console.error('生成OTA透视表时出错:', error);
                return { headers: [], rows: [] };
            }
        }

        /**
         * @function generateDriverPivot - 生成司机与车型分析透视表
         * @param {Array} data - 数据数组
         * @returns {Object} 透视表数据
         */
        function generateDriverPivot(data) {
            try {
                // 检查司机与车型分析的必需字段
                const fields = Object.keys(data[0] || {});
                const requiredFields = ['Driver', 'Car Type', 'OTA Price (RM)', 'Price (RM)'];
                const missingFields = requiredFields.filter(field => !fields.includes(field));

                if (missingFields.length > 0) {
                    console.log('司机与车型分析缺少字段:', missingFields);
                    return {
                        headers: ['提示'],
                        rows: [{ message: '无数据可显示 - 缺少必需字段' }],
                        noData: true
                    };
                }

                // 使用固定字段名
                const driverField = 'Driver';
                const carTypeField = 'Car Type';
                const otaPriceField = 'OTA Price (RM)';
                const priceField = 'Price (RM)';

                // 应用司机筛选（多选）
                let selectedDrivers = [];
                if (multiSelectInstances.driverFilter) {
                    selectedDrivers = multiSelectInstances.driverFilter.getSelectedValues();
                }

                if (selectedDrivers.length > 0) {
                    data = data.filter(row => selectedDrivers.includes(row[driverField]));
                }

                const showCarType = document.getElementById('carTypeColumn')?.checked || false;
                const grouped = new Map(); // 使用Map提高性能

                // 计算总计数和总价格用于占比计算
                const totalCount = data.length;
                let totalPrice = 0;

                // 分批计算总价格，避免大数组的reduce操作
                const batchSize = 1000;
                for (let i = 0; i < data.length; i += batchSize) {
                    const batch = data.slice(i, i + batchSize);
                    totalPrice += batch.reduce((sum, row) => {
                        return sum + (parseFloat(row[priceField]) || 0);
                    }, 0);
                }

                // 分批处理数据
                for (let i = 0; i < data.length; i += batchSize) {
                    const batch = data.slice(i, i + batchSize);

                    batch.forEach(row => {
                        const driver = row[driverField] || '未知';
                        const carType = row[carTypeField] || '未知';
                        const otaPrice = parseFloat(row[otaPriceField]) || 0;
                        const price = parseFloat(row[priceField]) || 0;

                        const key = showCarType ? `${driver}|${carType}` : driver;

                        if (!grouped.has(key)) {
                            grouped.set(key, {
                                driver: driver,
                                carType: showCarType ? carType : null,
                                count: 0,
                                otaPrice: 0,
                                price: 0
                            });
                        }

                        const item = grouped.get(key);
                        item.count++;
                        item.otaPrice += otaPrice;
                        item.price += price;
                    });
                }

                // 计算利润率、占比并排序
                const result = Array.from(grouped.values()).map(item => ({
                    ...item,
                    profitRate: item.otaPrice > 0 ? (1 - (item.price / item.otaPrice)) * 100 : 0,
                    countPercentage: totalCount > 0 ? (item.count / totalCount) * 100 : 0,
                    pricePercentage: totalPrice > 0 ? (item.price / totalPrice) * 100 : 0
                })).sort((a, b) => b.count - a.count);

                // 生成固定表头
                const headers = ['Driver'];
                if (showCarType) {
                    headers.push('Car Type');
                }
                headers.push('计数', '计数占比 (%)', 'OTA Price (RM)', 'Price (RM)', 'Price占比 (%)', '利润率 (%)');

                // 占比计算：在总计数和总Price (RM)中的占比
                // 所有数值按从高到低排序

                return {
                    headers,
                    rows: result
                };
            } catch (error) {
                console.error('生成司机透视表时出错:', error);
                return { headers: [], rows: [] };
            }
        }

        /**
         * @function generateRegionPivot - 生成区域分布分析透视表
         * @param {Array} data - 数据数组
         * @returns {Object} 透视表数据
         */
        function generateRegionPivot(data) {
            try {
                console.log('开始生成区域分布分析透视表，数据行数:', data.length);

                // 检查区域分布分析的必需字段（按用户要求只使用Car Type和Duty Regions）
                const fields = Object.keys(data[0] || {});

                // 智能字段匹配 - 查找车型字段
                const carTypeField = findFieldByPatterns(fields, [
                    'Car Type', 'CarType', 'Vehicle Type', 'VehicleType',
                    'Car_Type', 'Vehicle_Type', '车型', '车辆类型'
                ]);

                // 智能字段匹配 - 查找区域字段
                const regionField = findFieldByPatterns(fields, [
                    'Duty Regions', 'DutyRegions', 'Duty Region', 'DutyRegion',
                    'Regions', 'Region', 'Area', 'Location', 'Zone',
                    '区域', '地区', '位置', '地点'
                ]);

                if (!carTypeField || !regionField) {
                    const missingFields = [];
                    if (!carTypeField) missingFields.push('车型字段 (Car Type/Vehicle Type等)');
                    if (!regionField) missingFields.push('区域字段 (Duty Regions/Region等)');

                    console.log('区域分布分析缺少字段:', missingFields);
                    console.log('可用字段:', fields);
                    return {
                        headers: ['提示'],
                        rows: [{ message: `无数据可显示 - 缺少必需字段: ${missingFields.join(', ')}` }],
                        noData: true
                    };
                }

                console.log('区域分布分析使用字段:', { carTypeField, regionField });

                // 应用区域文本筛选
                const regionTextFilter = document.getElementById('regionTextFilter')?.value?.trim();

                if (regionTextFilter) {
                    data = data.filter(row => {
                        const regionValue = row[regionField] || '';
                        const carTypeValue = row[carTypeField] || '';
                        // 支持对区域和车型字段进行筛选
                        return regionValue.toLowerCase().includes(regionTextFilter.toLowerCase()) ||
                               carTypeValue.toLowerCase().includes(regionTextFilter.toLowerCase());
                    });
                    console.log(`区域文本筛选 "${regionTextFilter}" 后的数据行数:`, data.length);
                }

                // 按车型和区域组合分组统计计数
                const grouped = {};

                data.forEach(row => {
                    const carType = row[carTypeField] || '未知';
                    const region = row[regionField] || '未知';

                    // 使用车型和区域的组合作为分组键
                    const key = `${carType}|${region}`;

                    if (!grouped[key]) {
                        grouped[key] = {
                            carType: carType,
                            region: region,
                            count: 0
                        };
                    }

                    grouped[key].count++;
                });

                // 按计数排序，然后按车型和区域排序
                const result = Object.values(grouped).sort((a, b) => {
                    if (b.count !== a.count) {
                        return b.count - a.count; // 首先按计数降序
                    }
                    if (a.carType !== b.carType) {
                        return a.carType.localeCompare(b.carType); // 然后按车型升序
                    }
                    return a.region.localeCompare(b.region); // 最后按区域升序
                });

                console.log('区域分布分析结果:', result.length, '个分组');

                return {
                    headers: ['车型', '区域', '计数'],
                    rows: result
                };
            } catch (error) {
                console.error('生成区域透视表时出错:', error);
                return {
                    headers: ['错误'],
                    rows: [{ message: '生成区域分布分析时出错: ' + error.message }],
                    noData: true
                };
            }
        }

        /**
         * @function generateDatePivot - 生成日期与备注分析透视表
         * @param {Array} data - 数据数组
         * @returns {Object} 透视表数据
         */
        function generateDatePivot(data) {
            try {
                // 检查日期与备注分析的必需字段（移除Given Date字段要求）
                const fields = Object.keys(data[0] || {});
                const requiredFields = ['Driver Name', 'Remark', 'Mark'];
                const missingFields = requiredFields.filter(field => !fields.includes(field));

                if (missingFields.length > 0) {
                    console.log('日期与备注分析缺少字段:', missingFields);
                    return {
                        headers: ['提示'],
                        rows: [{ message: '无数据可显示 - 缺少必需字段' }],
                        noData: true
                    };
                }

                // 使用智能字段匹配 - 查找日期字段（可选）
                const dateField = findFieldByPatterns(fields, [
                    'Given Date', 'GivenDate', 'Date', 'Created Date', 'CreatedDate',
                    'Pickup Date', 'PickupDate', 'Trip Date', 'TripDate',
                    '日期', '创建日期', '接单日期', '行程日期'
                ]);

                const driverNameField = 'Driver Name';
                const remarkField = 'Remark';
                const markField = 'Mark';

                // 应用日期区间筛选（仅当日期字段存在时）
                if (dateField) {
                    const startDate = document.getElementById('startDate')?.value;
                    const endDate = document.getElementById('endDate')?.value;

                    if (startDate || endDate) {
                        data = data.filter(row => {
                            const rowDate = row[dateField];
                            if (!rowDate) return false;

                            let isInRange = true;
                            if (startDate && rowDate < startDate) {
                                isInRange = false;
                            }
                            if (endDate && rowDate > endDate) {
                                isInRange = false;
                            }
                            return isInRange;
                        });
                    }
                }

            const grouped = {};

            data.forEach(row => {
                const driverName = row[driverNameField] || '未知';
                const remark = row[remarkField] || '无备注';
                const mark = row[markField] || '未标记';

                // 不包含日期信息，固定使用三个字段分组
                const key = `${driverName}|${remark}|${mark}`;

                if (!grouped[key]) {
                    grouped[key] = {
                        driverName: driverName,
                        remark: remark,
                        mark: mark,
                        count: 0
                    };
                }

                grouped[key].count++;
            });

            // 按计数排序
            const result = Object.values(grouped).sort((a, b) => b.count - a.count);

            // 固定表头，不显示日期列
            const headers = [driverNameField, remarkField, markField, '计数'];

            return {
                headers: headers,
                rows: result
            };
            } catch (error) {
                console.error('生成日期透视表时出错:', error);
                return { headers: [], rows: [] };
            }
        }

        /**
         * @function generateCustomPivot - 生成自定义透视表
         */
        function generateCustomPivot() {
            try {
                console.log('开始生成自定义透视表');

                const customRowsSelect = document.getElementById('customRows');
                const customColumnsSelect = document.getElementById('customColumns');
                const customValuesSelect = document.getElementById('customValues');

                if (!customRowsSelect || !customColumnsSelect || !customValuesSelect) {
                    console.error('自定义透视表选择器元素未找到');
                    showError('自定义透视表配置元素未找到');
                    return;
                }

                const rowFields = Array.from(customRowsSelect.selectedOptions).map(opt => opt.value).filter(v => v);
                const columnFields = Array.from(customColumnsSelect.selectedOptions).map(opt => opt.value).filter(v => v);
                const valueFields = Array.from(customValuesSelect.selectedOptions).map(opt => opt.value).filter(v => v);

                console.log('自定义透视表字段选择:', { rowFields, columnFields, valueFields });

                if (rowFields.length === 0) {
                    showError('请至少选择一个行字段');
                    return;
                }

                if (!filteredData || filteredData.length === 0) {
                    showError('没有可用数据生成透视表');
                    return;
                }

                const pivotData = generateCustomPivotData(filteredData, rowFields, columnFields, valueFields);
                currentPivotData['custom'] = pivotData;
                renderPivotTable('custom', pivotData);

                console.log('自定义透视表生成完成');

            } catch (error) {
                console.error('生成自定义透视表时出错:', error);
                showError('生成自定义透视表时出错: ' + error.message);
            }
        }

        /**
         * @function generateCustomPivotData - 生成自定义透视表数据
         * @param {Array} data - 数据数组
         * @param {Array} rowFields - 行字段
         * @param {Array} columnFields - 列字段
         * @param {Array} valueFields - 值字段
         * @returns {Object} 透视表数据
         */
        function generateCustomPivotData(data, rowFields, columnFields, valueFields) {
            try {
                console.log('生成自定义透视表数据:', { rowFields, columnFields, valueFields });

                if (!data || data.length === 0) {
                    console.log('自定义透视表：无数据');
                    return {
                        headers: ['提示'],
                        rows: [{ message: '无数据可显示' }],
                        noData: true
                    };
                }

                const grouped = {};
                const columnValues = new Set();

                data.forEach(row => {
                    const rowKey = rowFields.map(field => row[field] || '未知').join('|');
                    const colKey = columnFields.length > 0 ?
                        columnFields.map(field => row[field] || '未知').join('|') : 'default';

                    if (columnFields.length > 0) {
                        columnValues.add(colKey);
                    }

                    const key = `${rowKey}|${colKey}`;

                    if (!grouped[key]) {
                        grouped[key] = {
                            rowKey: rowKey,
                            colKey: colKey,
                            count: 0,
                            values: {}
                        };

                        valueFields.forEach(field => {
                            grouped[key].values[field] = 0;
                        });
                    }

                    grouped[key].count++;
                    valueFields.forEach(field => {
                        const value = parseFloat(row[field]) || 0;
                        grouped[key].values[field] += value;
                    });
                });

                // 构建表头 - 确保表头正确显示
                const headers = [...rowFields];
                const sortedColumns = Array.from(columnValues).sort();

                console.log('自定义透视表表头构建:', { headers, sortedColumns, columnFields, valueFields });

                if (columnFields.length > 0) {
                    sortedColumns.forEach(col => {
                        headers.push(`${col}_计数`);
                        valueFields.forEach(field => {
                            headers.push(`${col}_${field}`);
                        });
                    });
                } else {
                    headers.push('计数');
                    valueFields.forEach(field => {
                        headers.push(field);
                    });
                }

                console.log('最终表头:', headers);

                const rowGroups = {};
                Object.values(grouped).forEach(item => {
                    if (!rowGroups[item.rowKey]) {
                        rowGroups[item.rowKey] = {};
                    }
                    rowGroups[item.rowKey][item.colKey] = item;
                });

                const rows = Object.keys(rowGroups).map(rowKey => {
                    const row = { rowKey: rowKey };
                    const rowParts = rowKey.split('|');
                    rowFields.forEach((field, index) => {
                        row[field] = rowParts[index];
                    });

                    if (columnFields.length > 0) {
                        sortedColumns.forEach(col => {
                            const item = rowGroups[rowKey][col];
                            row[`${col}_计数`] = item ? item.count : 0;
                            valueFields.forEach(field => {
                                row[`${col}_${field}`] = item ? item.values[field] : 0;
                            });
                        });
                    } else {
                        const item = rowGroups[rowKey]['default'];
                        row['计数'] = item ? item.count : 0;
                        valueFields.forEach(field => {
                            row[field] = item ? item.values[field] : 0;
                        });
                    }

                    return row;
                });

                console.log('自定义透视表数据生成完成:', { headers, rowCount: rows.length });
                return { headers, rows };

            } catch (error) {
                console.error('生成自定义透视表数据时出错:', error);
                return {
                    headers: ['错误'],
                    rows: [{ message: '生成透视表时出错: ' + error.message }],
                    noData: true
                };
            }
        }

        /**
         * @function renderPivotTable - 渲染透视表
         * @param {string} type - 透视表类型
         * @param {Object} pivotData - 透视表数据
         */
        function renderPivotTable(type, pivotData) {
            const container = document.getElementById(`${type}-table`);

            if (!container) {
                console.error(`找不到容器: ${type}-table`);
                return;
            }

            if (!pivotData || !pivotData.rows || pivotData.rows.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            // 处理"无数据可显示"的情况
            if (pivotData.noData) {
                container.innerHTML = '<div class="no-data-message">无数据可显示 - 缺少必需字段</div>';
                return;
            }

            try {
                // 对于大数据集，使用分页显示
                const maxRowsPerPage = 1000;
                const totalRows = pivotData.rows.length;
                const needsPagination = totalRows > maxRowsPerPage;

                let html = '';

                if (needsPagination) {
                    html += `<div class="pagination-info">显示前 ${maxRowsPerPage} 行，共 ${totalRows} 行数据</div>`;
                }

                html += '<div class="table-container"><table class="pivot-table"><thead><tr>';

                // 渲染表头 - 确保表头正确显示
                if (pivotData.headers && pivotData.headers.length > 0) {
                    pivotData.headers.forEach(header => {
                        html += `<th>${escapeHtml(header)}</th>`;
                    });
                } else {
                    console.error('表头数据为空或未定义:', pivotData.headers);
                    html += '<th>无表头数据</th>';
                }
                html += '</tr></thead><tbody>';

                // 渲染数据行（限制显示行数）
                const rowsToRender = needsPagination ? pivotData.rows.slice(0, maxRowsPerPage) : pivotData.rows;

                rowsToRender.forEach((row, index) => {
                    html += '<tr>';
                    pivotData.headers.forEach(header => {
                        let value = '';
                        let cellClass = '';

                        try {
                            switch (type) {
                                case 'ota':
                                    value = getOTAValue(row, header);
                                    cellClass = getOTACellClass(header);
                                    break;
                                case 'driver':
                                    value = getDriverValue(row, header);
                                    cellClass = getDriverCellClass(header);
                                    break;
                                case 'region':
                                    value = getRegionValue(row, header);
                                    cellClass = getRegionCellClass(header);
                                    break;
                                case 'date':
                                    value = getDateValue(row, header);
                                    cellClass = getDateCellClass(header);
                                    break;
                                case 'custom':
                                    value = getCustomValue(row, header);
                                    cellClass = getCustomCellClass(header);
                                    break;
                            }
                        } catch (error) {
                            console.error(`渲染单元格时出错 (行${index}, 列${header}):`, error);
                            value = '错误';
                        }

                        html += `<td class="${cellClass}">${escapeHtml(String(value))}</td>`;
                    });
                    html += '</tr>';
                });

                // 添加总计行
                const totalRow = calculateTotalRow(pivotData.rows, pivotData.headers, type);
                if (totalRow) {
                    console.log(`为${type}透视表添加总计行`);
                    html += '<tr class="total-row">';
                    pivotData.headers.forEach((header, index) => {
                        let value = '';
                        let cellClass = 'total-cell';

                        if (index === 0) {
                            cellClass += ' total-label';
                        }

                        try {
                            // 直接使用总计行数据，避免重复调用getValue函数
                            if (totalRow.hasOwnProperty(header)) {
                                value = totalRow[header];

                                // 格式化数值显示
                                if (typeof value === 'number' && !isNaN(value)) {
                                    if (header.includes('率') || header.includes('%') || header.includes('占比')) {
                                        value = formatPercentage(value);
                                    } else if (header.includes('Price') || header.includes('价格')) {
                                        value = formatCurrency(value);
                                    } else if (header === '计数' || header.includes('计数')) {
                                        value = Math.round(value); // 计数应该是整数
                                    } else {
                                        value = formatNumber(value);
                                    }
                                }
                            } else {
                                // 如果总计行中没有该字段，尝试使用getValue函数
                                switch (type) {
                                    case 'ota':
                                        value = getOTAValue(totalRow, header);
                                        break;
                                    case 'driver':
                                        value = getDriverValue(totalRow, header);
                                        break;
                                    case 'region':
                                        value = getRegionValue(totalRow, header);
                                        break;
                                    case 'date':
                                        value = getDateValue(totalRow, header);
                                        break;
                                    case 'custom':
                                        value = getCustomValue(totalRow, header);
                                        break;
                                    default:
                                        value = '';
                                }
                            }
                        } catch (error) {
                            console.error(`渲染总计行单元格时出错 (列${header}):`, error);
                            value = totalRow[header] || '';
                        }

                        html += `<td class="${cellClass}">${escapeHtml(String(value))}</td>`;
                    });
                    html += '</tr>';
                    console.log(`${type}透视表总计行添加完成`);
                } else {
                    console.log(`${type}透视表无总计行数据`);
                }

                html += '</tbody></table></div>';

                if (needsPagination) {
                    html += '<div class="pagination-note">注意：为了性能考虑，仅显示前1000行数据。使用筛选器可以查看特定数据。</div>';
                }

                container.innerHTML = html;

            } catch (error) {
                console.error('渲染透视表时出错:', error);
                container.innerHTML = `<div class="error">渲染表格时出错: ${error.message}</div>`;
            }
        }

        /**
         * @function escapeHtml - 转义HTML特殊字符
         * @param {string} text - 要转义的文本
         * @returns {string} 转义后的文本
         */
        function escapeHtml(text) {
            if (typeof text !== 'string') return text;
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * @function getOTAValue - 获取OTA透视表单元格值
         * @param {Object} row - 数据行
         * @param {string} header - 列头
         * @returns {string} 格式化的值
         */
        function getOTAValue(row, header) {
            // 动态处理表头
            if (header === '计数') {
                return row.count || 0;
            } else if (header === '利润率 (%)') {
                return formatPercentage(row.profitRate);
            } else if (header.toLowerCase().includes('price') || header.toLowerCase().includes('价格')) {
                if (header.toLowerCase().includes('ota')) {
                    return formatCurrency(row.otaPrice);
                } else {
                    return formatCurrency(row.price);
                }
            } else if (header.toLowerCase().includes('date') || header.toLowerCase().includes('日期')) {
                return row.pickupDate || '';
            } else {
                // 默认返回OTA字段
                return row.ota || '';
            }
        }

        /**
         * @function getDriverValue - 获取司机透视表单元格值
         * @param {Object} row - 数据行
         * @param {string} header - 列头
         * @returns {string} 格式化的值
         */
        function getDriverValue(row, header) {
            // 动态处理表头
            if (header === '计数') {
                return row.count || 0;
            } else if (header === '计数占比 (%)') {
                return formatPercentage(row.countPercentage);
            } else if (header === 'Price占比 (%)') {
                return formatPercentage(row.pricePercentage);
            } else if (header === '利润率 (%)') {
                return formatPercentage(row.profitRate);
            } else if (header.toLowerCase().includes('price') || header.toLowerCase().includes('价格')) {
                if (header.toLowerCase().includes('ota')) {
                    return formatCurrency(row.otaPrice);
                } else {
                    return formatCurrency(row.price);
                }
            } else if (header.toLowerCase().includes('car') || header.toLowerCase().includes('车型') || header.toLowerCase().includes('车辆')) {
                return row.carType || '';
            } else {
                // 默认返回司机字段
                return row.driver || '';
            }
        }

        /**
         * @function getRegionValue - 获取区域透视表单元格值
         * @param {Object} row - 数据行
         * @param {string} header - 列头
         * @returns {string} 格式化的值
         */
        function getRegionValue(row, header) {
            // 动态处理表头 - 适配车型和区域组合格式
            if (header === '计数') {
                return row.count || 0;
            } else if (header === '车型') {
                return row.carType || '';
            } else if (header === '区域') {
                return row.region || '';
            } else {
                // 对于其他字段，直接返回行数据中的值
                return row[header] || '';
            }
        }

        /**
         * @function getDateValue - 获取日期透视表单元格值
         * @param {Object} row - 数据行
         * @param {string} header - 列头
         * @returns {string} 格式化的值
         */
        function getDateValue(row, header) {
            // 动态处理表头
            if (header === '计数') {
                return row.count || 0;
            } else if (header.toLowerCase().includes('date') || header.toLowerCase().includes('日期')) {
                // 只有当行数据中确实有givenDate字段时才返回
                return row.givenDate || '';
            } else if (header === 'Driver Name' || header.toLowerCase().includes('driver') || header.toLowerCase().includes('司机')) {
                return row.driverName || '';
            } else if (header === 'Remark' || header.toLowerCase().includes('remark') || header.toLowerCase().includes('备注')) {
                return row.remark || '';
            } else if (header === 'Mark' || header.toLowerCase().includes('mark') || header.toLowerCase().includes('标记')) {
                return row.mark || '';
            } else {
                // 对于其他字段，直接返回行数据中的值
                return row[header] || '';
            }
        }

        /**
         * @function getCustomValue - 获取自定义透视表单元格值
         * @param {Object} row - 数据行
         * @param {string} header - 列头
         * @returns {string} 格式化的值
         */
        function getCustomValue(row, header) {
            const value = row[header];
            if (typeof value === 'number') {
                if (header.includes('计数')) {
                    return value;
                } else if (header.includes('%') || header.includes('率')) {
                    return formatPercentage(value);
                } else if (header.includes('Price') || header.includes('价格')) {
                    return formatCurrency(value);
                } else {
                    return formatNumber(value);
                }
            }
            return value || '';
        }

        /**
         * @function getCellClass - 获取单元格CSS类
         */
        function getOTACellClass(header) {
            if (header.includes('Price') || header.includes('计数')) return 'number-cell';
            if (header.includes('%') || header.includes('率')) return 'percentage-cell';
            return '';
        }

        function getDriverCellClass(header) {
            if (header.includes('Price') || header.includes('计数')) return 'number-cell';
            if (header.includes('%') || header.includes('率')) return 'percentage-cell';
            return '';
        }

        function getRegionCellClass(header) {
            if (header === '计数') return 'number-cell';
            return '';
        }

        function getDateCellClass(header) {
            if (header === '计数') return 'number-cell';
            return '';
        }

        function getCustomCellClass(header) {
            if (typeof header === 'string') {
                if (header.includes('计数') || header.includes('Price') || header.includes('价格')) return 'number-cell';
                if (header.includes('%') || header.includes('率')) return 'percentage-cell';
            }
            return '';
        }

        /**
         * @function formatCurrency - 格式化货币
         * @param {number} value - 数值
         * @returns {string} 格式化的货币字符串
         */
        function formatCurrency(value) {
            if (typeof value !== 'number' || isNaN(value)) return '0.00';
            return value.toFixed(2);
        }

        /**
         * @function formatPercentage - 格式化百分比
         * @param {number} value - 数值
         * @returns {string} 格式化的百分比字符串
         */
        function formatPercentage(value) {
            if (typeof value !== 'number' || isNaN(value)) return '0.00%';
            return value.toFixed(2) + '%';
        }

        /**
         * @function formatNumber - 格式化数字
         * @param {number} value - 数值
         * @returns {string} 格式化的数字字符串
         */
        function formatNumber(value) {
            if (typeof value !== 'number' || isNaN(value)) return '0';
            return value.toLocaleString();
        }

        /**
         * @function copyPivotTable - 复制透视表到剪贴板
         * @param {string} type - 透视表类型
         */
        async function copyPivotTable(type) {
            const pivotData = currentPivotData[type];
            if (!pivotData || !pivotData.rows || pivotData.rows.length === 0) {
                showError('没有可复制的数据');
                return;
            }

            try {
                // 构建制表符分隔的文本
                let text = pivotData.headers.join('\t') + '\n';

                pivotData.rows.forEach(row => {
                    const rowData = pivotData.headers.map(header => {
                        let value = '';
                        switch (type) {
                            case 'ota':
                                value = getOTAValue(row, header);
                                break;
                            case 'driver':
                                value = getDriverValue(row, header);
                                break;
                            case 'region':
                                value = getRegionValue(row, header);
                                break;
                            case 'date':
                                value = getDateValue(row, header);
                                break;
                            case 'custom':
                                value = getCustomValue(row, header);
                                break;
                        }
                        return value.toString().replace(/\t/g, ' ');
                    });
                    text += rowData.join('\t') + '\n';
                });

                await navigator.clipboard.writeText(text);
                showSuccess('透视表已复制到剪贴板！可以直接粘贴到Excel中。');
            } catch (error) {
                console.error('复制失败:', error);
                showError('复制失败，请手动选择表格内容进行复制');
            }
        }

        /**
         * @function showLoading - 显示加载状态
         * @param {string} message - 加载消息
         */
        function showLoading(message) {
            const container = document.querySelector('.upload-section');
            container.innerHTML = `<div class="loading">${message}</div>`;
        }

        /**
         * @function showError - 显示错误消息
         * @param {string} message - 错误消息
         */
        function showError(message) {
            console.error('显示错误消息:', message);

            const container = document.querySelector('.upload-section');
            if (!container) {
                console.error('找不到upload-section容器');
                return;
            }

            // 移除现有错误消息
            const existingError = container.querySelector('.error');
            if (existingError) {
                existingError.remove();
            }

            const uploadArea = container.querySelector('.upload-area');
            if (uploadArea) {
                container.insertAdjacentHTML('afterbegin', `<div class="error">${message}</div>`);

                // 确保错误消息显示
                const errorDiv = container.querySelector('.error');
                if (errorDiv) {
                    console.log('错误消息已显示:', message);

                    // 5秒后自动移除
                    setTimeout(() => {
                        if (errorDiv && errorDiv.parentNode) {
                            errorDiv.remove();
                        }
                    }, 5000);
                } else {
                    console.error('错误消息显示失败');
                }
            } else {
                // 如果没有upload-area，重新创建整个结构
                container.innerHTML = `
                    <div class="error">${message}</div>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-text">📁 点击或拖拽Excel文件到此处</div>
                        <div class="upload-hint">支持 .xlsx 和 .xls 格式文件</div>
                        <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls,.csv">
                    </div>
                `;

                // 重新绑定事件
                const newUploadArea = document.getElementById('uploadArea');
                const newFileInput = document.getElementById('fileInput');
                if (newUploadArea && newFileInput) {
                    newUploadArea.addEventListener('click', () => newFileInput.click());
                    newUploadArea.addEventListener('dragover', handleDragOver);
                    newUploadArea.addEventListener('drop', handleFileDrop);
                    newFileInput.addEventListener('change', handleFileSelect);
                }
            }
        }

        /**
         * @function showSuccess - 显示成功消息
         * @param {string} message - 成功消息
         */
        function showSuccess(message) {
            const container = document.querySelector('.upload-section');
            const uploadArea = container.querySelector('.upload-area');
            if (uploadArea) {
                container.insertAdjacentHTML('afterbegin', `<div class="success">${message}</div>`);
                setTimeout(() => {
                    const successDiv = container.querySelector('.success');
                    if (successDiv) successDiv.remove();
                }, 3000);
            }
        }

        /**
         * @function selectAllRegions - 全选区域
         */
        function selectAllRegions() {
            if (multiSelectInstances.regionFilter) {
                multiSelectInstances.regionFilter.selectAll();
            }
        }

        /**
         * @function clearAllRegions - 清空区域选择
         */
        function clearAllRegions() {
            if (multiSelectInstances.regionFilter) {
                multiSelectInstances.regionFilter.clearAll();
            }
        }

        /**
         * @function selectAllOTA - 全选OTA
         */
        function selectAllOTA() {
            if (multiSelectInstances.otaFilter) {
                multiSelectInstances.otaFilter.selectAll();
            }
        }

        /**
         * @function clearAllOTA - 清空OTA选择
         */
        function clearAllOTA() {
            if (multiSelectInstances.otaFilter) {
                multiSelectInstances.otaFilter.clearAll();
            }
        }

        /**
         * @function initializeMultiSelectComponents - 初始化多选组件
         */
        function initializeMultiSelectComponents() {
            // 初始化区域筛选器
            multiSelectInstances.regionFilter = new MultiSelectDropdown('regionFilterContainer', {
                placeholder: '所有区域',
                onChange: (selectedValues) => {
                    applyGlobalFilter();
                }
            });

            // 初始化OTA筛选器
            multiSelectInstances.otaFilter = new MultiSelectDropdown('otaFilterContainer', {
                placeholder: '所有OTA',
                onChange: (selectedValues) => {
                    generatePivotTable('ota');
                }
            });

            // 初始化司机筛选器（司机与车型分析标签页）
            multiSelectInstances.driverFilter = new MultiSelectDropdown('driverFilterContainer', {
                placeholder: '所有司机',
                onChange: (selectedValues) => {
                    generatePivotTable('driver');
                }
            });

            // 区域分布分析使用文本筛选器，不需要多选下拉菜单
            // 文本筛选器通过oninput事件直接触发筛选
        }

        /**
         * @function calculateTotalRow - 计算透视表总计行
         * @param {Array} rows - 数据行数组
         * @param {Array} headers - 表头数组
         * @param {string} type - 透视表类型
         * @returns {Object} 总计行数据
         */
        function calculateTotalRow(rows, headers, type) {
            if (!rows || rows.length === 0) {
                console.log('无数据行，跳过总计行计算');
                return null;
            }

            console.log(`开始计算${type}透视表总计行，数据行数:`, rows.length);
            const totalRow = {};

            headers.forEach((header, index) => {
                if (index === 0) {
                    // 第一列显示"总计"标识
                    totalRow[header] = '总计';
                } else if (isNumericColumn(header)) {
                    // 数值列计算总和或平均值
                    let sum = 0;
                    let validCount = 0;

                    rows.forEach(row => {
                        let value = 0;
                        try {
                            // 优先使用行数据中的直接属性
                            if (row.hasOwnProperty(header)) {
                                value = parseFloat(row[header]) || 0;
                            } else {
                                // 如果直接属性不存在，则根据透视表类型获取值
                                switch (type) {
                                    case 'ota':
                                        const otaValue = getOTAValue(row, header);
                                        value = parseFloat(otaValue) || 0;
                                        break;
                                    case 'driver':
                                        const driverValue = getDriverValue(row, header);
                                        value = parseFloat(driverValue) || 0;
                                        break;
                                    case 'region':
                                        const regionValue = getRegionValue(row, header);
                                        value = parseFloat(regionValue) || 0;
                                        break;
                                    case 'date':
                                        const dateValue = getDateValue(row, header);
                                        value = parseFloat(dateValue) || 0;
                                        break;
                                    case 'custom':
                                        const customValue = getCustomValue(row, header);
                                        value = parseFloat(customValue) || 0;
                                        break;
                                    default:
                                        value = 0;
                                }
                            }

                            if (!isNaN(value) && value !== 0) {
                                validCount++;
                            }
                            sum += value;
                        } catch (error) {
                            console.error(`计算总计时出错 (${header}):`, error);
                        }
                    });

                    // 对于百分比和比率字段，计算平均值而不是总和
                    if (header.includes('率') || header.includes('%') || header.includes('占比')) {
                        totalRow[header] = validCount > 0 ? sum / validCount : 0;
                    } else {
                        totalRow[header] = sum;
                    }

                    console.log(`${header} 总计: ${totalRow[header]} (有效数据: ${validCount})`);
                } else {
                    // 文本列显示空
                    totalRow[header] = '';
                }
            });

            console.log('计算的总计行:', totalRow);
            return totalRow;
        }

        /**
         * @function isNumericColumn - 判断是否为数值列
         * @param {string} header - 列头名称
         * @returns {boolean} 是否为数值列
         */
        function isNumericColumn(header) {
            const numericPatterns = [
                '计数', 'count', 'Count',
                'Price', '价格', '金额',
                '利润率', '占比', '率', '%',
                '总计', 'Total', 'Sum'
            ];

            return numericPatterns.some(pattern =>
                header.includes(pattern) || header.toLowerCase().includes(pattern.toLowerCase())
            );
        }

        /**
         * @function findFieldByPatterns - 根据模式查找字段
         * @param {Array} fields - 可用字段列表
         * @param {Array} patterns - 匹配模式列表
         * @returns {string|null} 匹配的字段名或null
         */
        function findFieldByPatterns(fields, patterns) {
            // 首先尝试精确匹配
            for (const pattern of patterns) {
                if (fields.includes(pattern)) {
                    return pattern;
                }
            }

            // 然后尝试不区分大小写的匹配
            for (const pattern of patterns) {
                const found = fields.find(field =>
                    field.toLowerCase() === pattern.toLowerCase()
                );
                if (found) {
                    return found;
                }
            }

            // 最后尝试包含匹配
            for (const pattern of patterns) {
                const found = fields.find(field =>
                    field.toLowerCase().includes(pattern.toLowerCase()) ||
                    pattern.toLowerCase().includes(field.toLowerCase())
                );
                if (found) {
                    return found;
                }
            }

            return null;
        }

        /**
         * @function initializeDateRangeFilter - 初始化日期区间选择器
         */
        function initializeDateRangeFilter() {
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');

            if (startDateInput && endDateInput) {
                // 获取数据中的日期范围
                const dates = rawData.map(row => row['Given Date']).filter(Boolean);
                if (dates.length > 0) {
                    const sortedDates = dates.sort();
                    const minDate = sortedDates[0];
                    const maxDate = sortedDates[sortedDates.length - 1];

                    // 设置默认日期范围
                    startDateInput.value = minDate;
                    endDateInput.value = maxDate;

                    console.log('日期区间初始化:', minDate, '到', maxDate);
                }
            }
        }

        /**
         * @function applyDateRangeFilter - 应用日期区间筛选
         */
        function applyDateRangeFilter() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (startDate && endDate && startDate > endDate) {
                showError('开始日期不能晚于结束日期');
                return;
            }

            console.log('应用日期区间筛选:', startDate, '到', endDate);
            generatePivotTable('date');
        }

        /**
         * @function clearDateRangeFilter - 清空日期区间筛选
         */
        function clearDateRangeFilter() {
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            console.log('清空日期区间筛选');
            generatePivotTable('date');
        }

        /**
         * @function applyRegionTextFilter - 应用区域文本筛选
         */
        function applyRegionTextFilter() {
            const filterText = document.getElementById('regionTextFilter')?.value?.trim();
            console.log('应用区域文本筛选:', filterText);
            generatePivotTable('region');
        }

        /**
         * @function clearRegionTextFilter - 清空区域文本筛选
         */
        function clearRegionTextFilter() {
            const filterInput = document.getElementById('regionTextFilter');
            if (filterInput) {
                filterInput.value = '';
                console.log('清空区域文本筛选');
                generatePivotTable('region');
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
